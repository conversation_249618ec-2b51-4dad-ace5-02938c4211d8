// ERP System Authorization Schema

// User definition
definition user {}

// Organization definition
definition organization {
    // Relations
    relation admin: user
    relation member: user
    relation viewer: user

    // Permissions
    permission view = viewer + member + admin
    permission edit = member + admin
    permission manage = admin
}

// Department definition
definition department {
    // Relations
    relation organization: organization
    relation manager: user
    relation employee: user
    relation viewer: user

    // Permissions
    permission view = viewer + employee + manager + organization->view
    permission edit = employee + manager + organization->edit
    permission manage = manager + organization->manage
}

// Project definition
definition project {
    // Relations
    relation department: department
    relation organization: organization
    relation owner: user
    relation contributor: user
    relation viewer: user

    // Permissions
    permission view = viewer + contributor + owner + department->view + organization->view
    permission edit = contributor + owner + department->edit + organization->edit
    permission manage = owner + department->manage + organization->manage
}

// Document definition
definition document {
    // Relations
    relation project: project
    relation department: department
    relation organization: organization
    relation owner: user
    relation editor: user
    relation viewer: user

    // Permissions
    permission view = viewer + editor + owner + project->view + department->view + organization->view
    permission edit = editor + owner + project->edit + department->edit + organization->edit
    permission delete = owner + project->manage + department->manage + organization->manage
}

// Financial Record definition
definition financial_record {
    // Relations
    relation organization: organization
    relation department: department
    relation creator: user
    relation approver: user
    relation viewer: user

    // Permissions
    permission view = viewer + creator + approver + department->view + organization->view
    permission edit = creator + department->edit + organization->edit
    permission approve = approver + department->manage + organization->manage
    permission delete = organization->manage
}