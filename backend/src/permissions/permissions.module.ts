import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PermissionsService } from './services/permissions.service';
import { SpiceDbService } from './services/spicedb.service';
import { PermissionsController } from './controllers/permissions.controller';

@Module({
  imports: [ConfigModule],
  providers: [PermissionsService, SpiceDbService],
  controllers: [PermissionsController],
  exports: [PermissionsService, SpiceDbService],
})
export class PermissionsModule {}
