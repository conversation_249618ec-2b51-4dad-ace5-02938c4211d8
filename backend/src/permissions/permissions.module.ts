import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { PermissionsController } from './controllers/permissions.controller'
import { PermissionsService } from './services/permissions.service'
import { SpiceDbService } from './services/spicedb.service'

@Module({
  imports: [
    ConfigModule,
  ],
  providers: [
    PermissionsService,
    // SpiceDbService, // Temporarily disabled
  ],
  controllers: [
    PermissionsController,
  ],
  exports: [
    PermissionsService,
    SpiceDbService,
  ],
})
export class PermissionsModule {}
