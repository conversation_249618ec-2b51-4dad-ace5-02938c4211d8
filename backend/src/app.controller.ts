import { Controller, Get } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import type { AppService } from './app.service.ts'

@ApiTags('Health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({
    summary: 'Health check endpoint',
  })
  getHello(): string {
    return this.appService.getHello()
  }

  @Get('health')
  @ApiOperation({
    summary: 'Application health status',
  })
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'massimo-erp-backend',
    }
  }
}
