import { createContext, use<PERSON>ontext, createSignal, createEffect, ParentComponent } from 'solid-js'
import { createStore } from 'solid-js/store'

export interface User {
  id: string
  email: string
  name: string
  roles: string[]
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface AuthActions {
  login: (email: string, password: string) => Promise<boolean>
  register: (name: string, email: string, password: string) => Promise<boolean>
  logout: () => void
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<[AuthState, AuthActions]>()

export const AuthProvider: ParentComponent = (props) => {
  const [authState, setAuthState] = createStore<AuthState>({
    user: null,
    token: localStorage.getItem('auth_token'),
    isAuthenticated: false,
    isLoading: true,
  })

  const API_BASE = 'http://localhost:3001'

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setAuthState('isLoading', true)
      
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        throw new Error('Login failed')
      }

      const data = await response.json()
      
      localStorage.setItem('auth_token', data.access_token)
      setAuthState({
        user: data.user,
        token: data.access_token,
        isAuthenticated: true,
        isLoading: false,
      })

      return true
    } catch (error) {
      console.error('Login error:', error)
      setAuthState('isLoading', false)
      return false
    }
  }

  const register = async (name: string, email: string, password: string): Promise<boolean> => {
    try {
      setAuthState('isLoading', true)
      
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password }),
      })

      if (!response.ok) {
        throw new Error('Registration failed')
      }

      // After successful registration, automatically login
      return await login(email, password)
    } catch (error) {
      console.error('Registration error:', error)
      setAuthState('isLoading', false)
      return false
    }
  }

  const logout = () => {
    localStorage.removeItem('auth_token')
    setAuthState({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
    })
  }

  const checkAuth = async () => {
    const token = localStorage.getItem('auth_token')
    
    if (!token) {
      setAuthState('isLoading', false)
      return
    }

    try {
      const response = await fetch(`${API_BASE}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error('Token invalid')
      }

      const user = await response.json()
      setAuthState({
        user,
        token,
        isAuthenticated: true,
        isLoading: false,
      })
    } catch (error) {
      console.error('Auth check error:', error)
      localStorage.removeItem('auth_token')
      setAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      })
    }
  }

  // Check authentication on mount
  createEffect(() => {
    checkAuth()
  })

  const actions: AuthActions = {
    login,
    register,
    logout,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={[authState, actions]}>
      {props.children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider')
  }
  return context
}
